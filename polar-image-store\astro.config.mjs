// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import cloudflare from '@astrojs/cloudflare';
import sitemap from '@astrojs/sitemap';
import compress from '@playform/compress';

// https://astro.build/config
export default defineConfig({
  site: 'https://infpik.store', // Required for sitemap
  output: 'server', // Changed from 'static' to 'server' to support API routes on Cloudflare Pages
  image: {
    // Allow remote image optimization from external sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com', // Amazon S3 where Polar.sh stores images
      },
      {
        protocol: 'https',
        hostname: 'polar.sh', // Polar.sh domain
      },
      {
        protocol: 'https',
        hostname: '**.polar.sh', // Polar.sh subdomains
      }
    ],
  },
  adapter: cloudflare({
    platformProxy: {
      enabled: true,
      configPath: 'wrangler.jsonc',
      persist: {
        path: './.cache/wrangler/v3'
      },
    },
    imageService: 'cloudflare',
  }),
  integrations: [
    sitemap({
      // Customize sitemap generation
      filter: (page) => !page.includes('/api/'),
      customPages: [],
      i18n: {
        defaultLocale: 'en',
        locales: {
          en: 'en-US',
        },
      },
    }),
    compress({
      CSS: true,           // ✅ Compress CSS with csso
      HTML: true,          // ✅ Compress HTML with html-minifier-terser
      JavaScript: true,    // ✅ Compress JavaScript with terser
      JSON: true,          // ✅ Compress JSON responses
      SVG: true,           // ✅ Compress SVG with svgo
      Image: false,        // ❌ Disable - already using Cloudflare Image Transform
      Logger: 1,           // Moderate logging level
    }),
  ],
  vite: {
    plugins: [tailwindcss()],
  },
});
